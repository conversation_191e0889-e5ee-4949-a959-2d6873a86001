"use client";

import React from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import "./CustomAccordion.scss";

interface AccordionDetail {
  title?: string;
  description?: string;
  caption?: string;
  bullets?: string;
}

interface CustomAccordionProps {
  label: string;
  details?: AccordionDetail[];
  defaultExpanded?: boolean;
  disabled?: boolean;
  index: number;
  expanded?: boolean;
  onChange?: (panel: number) => void;
}

const CustomAccordion: React.FC<CustomAccordionProps> = ({
  label,
  details,
  defaultExpanded = false,
  disabled = false,
  index,
  expanded,
  onChange,
}) => {
  const handleChange = (_event: React.SyntheticEvent, isExpanded: boolean) => {
    if (onChange) {
      onChange(index);
    }
  };

  return (
    <Accordion
      className="custom-accordion-item"
      expanded={expanded === undefined ? defaultExpanded : expanded}
      onChange={handleChange}
      disabled={disabled}
      sx={{
        background: "transparent",
        "&.Mui-disabled": {
          background: "transparent",
          opacity: 1,
        },
      }}
    >
      <AccordionSummary
        className="custom-accordion-summary"
        aria-controls={`panel${index}-content`}
        id={`panel${index}-header`}
        expandIcon={
          details && details.length > 0 ? (
            <ExpandMoreIcon sx={{ color: "#8CFFE4" }} />
          ) : null
        }
      >
        <Typography className="custom-accordion-label">{label}</Typography>
      </AccordionSummary>
      <AccordionDetails className="custom-accordion-details">
        {details?.map((detail, idx) => (
          <Box
            key={idx}
            className="detail-item"
            sx={{ mb: 2 }}
            // data-aos="fade-up"
            // data-aos-delay={150 * (idx + 1)}
          >
            <Typography variant="h6" className="detail-title" sx={{ mb: 1 }}>
              {detail.title}
            </Typography>
            <Typography variant="body1" className="detail-description">
              {detail.description}
            </Typography>
            {/* <List className="Bullets-points">
              {detail.bullets.split(". ").map((point, pointIndex) => (
                <ListItem key={pointIndex} className="bullet-point">
                  <ListItemText primary={point.trim()} />
                </ListItem>
              ))}
            </List> */}

            <Typography className="caption">{detail.caption}</Typography>
          </Box>
        ))}
      </AccordionDetails>
    </Accordion>
  );
};

export default CustomAccordion;
