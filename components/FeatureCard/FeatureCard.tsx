"use client";

import React from "react";
import "./FeatureCard.scss";
import { Box, Typography } from "@mui/material";

interface FeatureCardProps {
  titleLine1: string;
  titleLine2: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ titleLine1, titleLine2 }) => {
  return (
    <Box className="feature-card">
      <Typography variant="h6" className="feature-card-text">
        {titleLine1}
        <br />
        {titleLine2}
      </Typography>
    </Box>
  );
};

export default FeatureCard;
