.ar-vr-page {
    .ar-vr-content {
        padding-left: 101px;
        padding-right: 101px;

        .ar-vr-image {
            display: flex;
            justify-content: center;
            align-items: center;


            img {
                width: 100%;
                min-width: 1231px;
                min-height: 674px;
                border-radius: 30px;
            }

            margin-bottom: 115px;
        }

        .ar-vr-text {
            margin-bottom: 65px;

            .ar-vr-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                color: #8CFFE4;
                margin-bottom: 50px;

            }

            .ar-vr-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #FFFFFF;
            }
        }

        .future-vision {
            margin-bottom: 70px;

            .future-vision-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;

                letter-spacing: 0%;
                vertical-align: middle;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%, #549989 100%);
                background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 28px;

            }

            .future-vision-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 50px;
            }

            .future-vision-bullets {
                .future-vision-bullets-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    vertical-align: middle;
                    color: #FFFFFF;

                }

                .future-vision-bullets-list {
                    margin-bottom: 69px;

                    .future-vision-bullets-item {
                        position: relative;
                        padding-left: 20px;
                        display: flex;
                        align-items: start;

                        &::before {
                            content: "•";
                            position: absolute;
                            left: 0;
                            color: #FFFFFF;
                            font-size: 20px;
                        }

                        .future-vision-bullets-title {
                            font-family: Poppins;
                            font-weight: 600;
                            font-size: 20px;
width: 100%;
                            line-height: 30px;
                            letter-spacing: 0%;
                            // text-align: justify;
                            // vertical-align: middle;
                            color: #FFFFFF;

                        }

                        .future-vision-bullets-text {
                            font-family: Poppins;
                            font-weight: 400;
                            font-size: 20px;

                            line-height: 30px;
                            letter-spacing: 0%;
                            text-align: justify;
                            // vertical-align: middle;
                            color: #FFFFFF;
                        }
                    }
                }

                .future-vision-conclusion {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                }
            }
        }

        .what-we-offer {
            margin-bottom: 70px;

            .what-we-offer-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;

                letter-spacing: 0%;
                vertical-align: middle;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%, #549989 100%);
                background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;

            }

            .what-we-offer-bullets {
                margin-bottom: 60px;

                .what-we-offer-item {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    vertical-align: middle;
                    color: #FFFFFF;

                    &::before {
                        content: "•";
                        position: absolute;
                        left: 0;
                        color: #FFFFFF;
                        font-size: 20px;
                    }
                }

            }

            .what-we-offer-conclusion {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
            }
        }

        .Creative-Design {
            .Creative-Design-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;

                letter-spacing: 0%;
                vertical-align: middle;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%, #549989 100%);
                background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 52px;
            }

            .Creative-Design-text {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 57px;
            }

            .Creative-Design-bullets-and-image {
                display: flex;
                justify-content: space-between;

                .Creative-Design-bullets {
                    .Creative-Design-bullets-title {
                        font-family: Poppins;
                        font-weight: 600;
                        font-size: 20px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        vertical-align: middle;
                        color: #FFFFFF;
                        margin-bottom: 20px;

                    }

                    .Creative-Design-bullets-list {
                        .Creative-Design-bullets-item {
                            .Creative-Design-bullets-text {
                                font-family: Poppins;
                                font-weight: 400;
                                font-size: 20px;

                                line-height: 40px;
                                letter-spacing: 0%;
                                vertical-align: middle;
                                color: #FFFFFF;
                            }

                            &::before {
                                content: "•";
                                position: absolute;
                                left: 0;
                                color: #FFFFFF;
                                font-size: 20px;
                            }


                        }
                    }


                }

                .Creative-Design-image {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 55px;

                    .Creative-Design-image-caption {
                        font-family: Poppins;
                        font-weight: 275 !important;
                        font-size: 24px;

                        letter-spacing: 0%;
                        text-align: center;
                        color: #FFFFFF;
                    }
                }
            }
        }
    }
}