.iot-page-container {
    .iot-page-content {
        .iot-header-image {
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 110px;

            img {
                width: 100%;
                max-width: 100%;
                height: auto;
            }
        }

        .iot-content {
            padding-left: 95px;
            padding-right: 95px;
            margin-bottom: 95px;

            .iot-title {
                margin-bottom: 33px;
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                background: #8CFFE4;
                background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .iot-description-container {
                display: flex;
                flex-direction: column;
                gap: 55px;
                margin-bottom: 118px;

                .iot-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;

                }
            }

            .iot-accordion-container {
                margin-bottom: 70px;

                .iot-title {
                    font-family: Poppins;
                    font-weight: 300;
                    font-size: 50px;

                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 40px;
                }
            }

            .worked-on-platforms {
                width: 100%;
                margin-bottom: 65px;


                .worked-on-platforms-title {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 34px;

                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 50px;
                }

                .worked-on-platforms-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                    margin-bottom: 29px;

                }

                .platforms-container {
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        width: 100%;
                    }
                }
            }



        }


        .product-design-image {

            .image-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;

                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 50px;

            }

            .image-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #FFFFFF;
                margin-bottom: 29px;
            }

            .image-container {
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;


                img {
                    margin-bottom: 28px;
                    width: 100%;
                }
            }

            .image-caption {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;


                letter-spacing: 0%;
                text-align: justify;
                color: #FFFFFF;
                margin-bottom: 70px;
            }

            .service-list-container {
                .service-list-title {
                    font-family: Poppins;
                    font-weight: 600;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    color: #FFFFFF;
                    margin-bottom: 50px;
                }

                .service-list {


                    .service-item {
                        position: relative;
                        padding-left: 20px;
                        align-items: flex-start;

                        &::before {
                            content: "•";
                            position: absolute;
                            left: 0;
                            color: #FFFFFF;
                            font-size: 20px;
                        }

                        .service-item-title {
                            font-family: Poppins;
                            font-weight: 600;
                            font-size: 20px;

                            line-height: 30px;
                            letter-spacing: 0%;
                            vertical-align: middle;
                            min-width: fit-content;
                            color: #FFFFFF;
                            margin-right: 10px;
                        }

                        .service-item-description {
                            font-family: Poppins;
                            font-weight: 400;
                            font-size: 20px;

                            line-height: 30px;
                            letter-spacing: 0%;
                            vertical-align: middle;
                            color: #FFFFFF;
                            margin-bottom: 43px;
                        }






                    }

                }
            }

            .image-container-4 {
                display: flex;
                flex-direction: column;
                align-items: center;

                img {
                    margin-bottom: 16px;
                }

                .image-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                    margin-bottom: 73px;
                }

                .image-caption {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: center;
                    vertical-align: middle;
                    color: #FFFFFF;
                }
            }


        }




    }
}