"use client";

import "./IotPage.scss";
import { Box, List, ListItem, Typography } from "@mui/material";
import Image from "next/image";
import { iot_im1, iot_im2, iot_im3, iot_im4 } from "@/public/index";
import CustomAccordion from "@/components/CustomAccordion/CustomAccordion";
import { IotAccordionItems } from "@/constant/index";
import { useState } from "react";

const IotPage = () => {
  const [expandedPanel, setExpandedPanel] = useState<number | false>(false);

  const handleAccordionChange = (panel: number) => {
    setExpandedPanel(expandedPanel === panel ? false : panel);
  };

  return (
    <Box className="iot-page-container">
      <Box className="iot-page-content">
        <Box className="iot-header-image" data-aos="fade-down">
          <Image src={iot_im1} alt="IOT Image 1" />
        </Box>

        <Box className="iot-content">
          <Box
            className="iot-description-container"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <Typography variant="body1" className="iot-description">
              Modern electronic systems rely on embedded technologies to create
              secure, connected, and intelligent devices that are designed for
              low-power consumption and efficient operation. Aadvik TekLabs
              exists as a distinctive design service organization which delivers
              full electronic system development solutions starting from
              hardware and progressing to network protocols and certificates.
            </Typography>
            <Typography variant="body1" className="iot-description">
              Aadvik Teklabs engineers are experienced in the field of hardware
              and software engineering to ensure the system functions
              efficiently of your product and associated application. Our team
              is specialize in delivering comprehensive Embedded Systems
              services to our clients through the entire electronic product
              development life cycle starting from PoC conceptualization stage
              to the certification testing & Compliance. Our expertise spans
              various industries, including Lighting, Consumer Electronics, Home
              Automation, Appliances, HVAC, Sensor Technology, Industrial
              Machinery, and Wearables.
            </Typography>
          </Box>

          <Box className="iot-accordion-container">
            <Typography variant="h2" className="iot-title">
              The versatility of our offerings are -
            </Typography>{" "}
            {IotAccordionItems.map((item, index) => (
              <CustomAccordion
                key={index}
                index={index}
                label={item.accordionLabel}
                details={item.details}
                defaultExpanded={false}
                expanded={expandedPanel === index}
                onChange={handleAccordionChange}
              />
            ))}
          </Box>

          <Box
            className="worked-on-platforms"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Typography variant="h2" className="worked-on-platforms-title">
              Embedded Platforms We Have Worked On
            </Typography>
            <Typography
              variant="body1"
              className="worked-on-platforms-description"
            >
              Our extensive experience encompasses a wide range of platforms,
              enabling us to deliver tailored solutions that meet the specific
              needs of our clients.
            </Typography>

            <Box
              data-aos="zoom-in"
              data-aos-delay="500"
              className="platforms-container"
            >
              <Image src={iot_im2} alt="iot Page Image 2" />
            </Box>
          </Box>

          <Box
            className="product-design-image"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <Typography
              className="image-title"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              IoT Communication & Network Stack
            </Typography>
            <Typography
              className="image-description"
              data-aos="fade-up"
              data-aos-delay="600"
            >
              Aadvik Teklabs recognize reliable connectivity and strong security
              are critical to the success of any IoT deployment. With the
              growing variety of wired and wireless communication technologies,
              choosing a right communication hardware is a key decision factor
              while rolling out the product in field. Our expertise lies in
              delivering comprehensive end-to-end connectivity and security
              solutions, enabling smooth and secure integration from edge
              devices and access points to cloud infrastructure.
            </Typography>

            <Box
              className="image-container"
              data-aos="zoom-in"
              data-aos-delay="600"
            >
              <Image src={iot_im3} alt="Branding Page Image 3" />

              <Typography className="image-caption">
                IoT-based wireless communication enables seamless connectivity
                between smart devices, improving automation, efficiency, and
                data-driven decision-making across industries. The choice of
                wireless technology depends on factors like range, power
                consumption, and data rate requirements.
              </Typography>
            </Box>

            <Box className="service-list-container">
              <Typography
                variant="h2"
                className="service-list-title"
                data-aos="fade-up"
                data-aos-delay="800"
              >
                Our Connectivity Services Include:
              </Typography>
              <List className="service-list">
                <ListItem
                  className="service-item"
                  data-aos="fade-up"
                  data-aos-delay="900"
                >
                  <Typography className="service-item-title">
                    Gateway Design & Development:
                  </Typography>
                  <Typography className="service-item-description">
                    We design and develop IoT gateways that facilitate seamless
                    communication between edge devices and cloud platforms.
                  </Typography>
                </ListItem>
                <ListItem
                  className="service-item"
                  data-aos="fade-up"
                  data-aos-delay="1000"
                >
                  <Typography className="service-item-title">
                    Network Connectivity:{" "}
                  </Typography>
                  <Typography className="service-item-description">
                    We identify and deploy the most suitable communication
                    technologies based on coverage, range, scalability, cost,
                    and deployment requirements, ensuring security across the
                    entire network.
                  </Typography>
                </ListItem>
                <ListItem
                  className="service-item"
                  data-aos="fade-up"
                  data-aos-delay="1100"
                >
                  <Typography className="service-item-title">
                    Network Stack Integration:
                  </Typography>{" "}
                  <Typography className="service-item-description">
                    We understand that implementing network stack is crucial to
                    design and develop a robust IoT solution. Our team is
                    experienced in various connectivity stack development like
                    OCPP, DLMS, OPC UA
                  </Typography>
                </ListItem>
                <ListItem
                  className="service-item"
                  data-aos="fade-up"
                  data-aos-delay="1200"
                >
                  <Typography className="service-item-title">
                    Standards/Open Source Support:
                  </Typography>{" "}
                  <Typography className="service-item-description">
                    We ensure compliance with industry standards and integrate
                    open-source solutions to provide flexible and cost-effective
                    connectivity options.
                  </Typography>
                </ListItem>
                <ListItem
                  className="service-item"
                  data-aos="fade-up"
                  data-aos-delay="1300"
                >
                  <Typography className="service-item-title">
                    Network Connectivity:
                  </Typography>{" "}
                  <Typography className="service-item-description">
                    We identify and deploy the most suitable communication
                    technologies based on coverage, range, scalability, cost,
                    and deployment requirements, ensuring security across the
                    entire network.
                  </Typography>
                </ListItem>
              </List>
            </Box>

            <Box className="image-container-4">
              <Image
                src={iot_im4}
                alt="iot Page Image 4"
                className="iot-image-4"
                data-aos="zoom-in"
                data-aos-delay="600"
              />
              <Typography
                className="image-description"
                data-aos="fade-up"
                data-aos-delay="600"
              >
                With growing era of green engineering market, Aadvik’s team
                excels at developing sustainable and efficient systems to meet
                your requirements with reliable security in place. Whether it is
                adding connectivity module to your Legacy or complete system
                design, we specialize in developing the electronic systems which
                can be your technology building blocks to address your customers
                needs, ensuring both environmental sustainability and optimal
                performance.
              </Typography>

              <Typography className="image-caption">
                Partner with us to navigate the complexities of IoT connectivity
                and security, and to deploy solutions that drive innovation and
                efficiency in your operations.
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default IotPage;
