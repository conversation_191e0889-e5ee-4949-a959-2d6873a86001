.mechanical-design-page-container {

    margin-bottom: 543px;

    .mechanical-design-page-content {

        .mechanical-design-header-image {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
                width: 100%;
                max-width: 100%;
                height: 90vh;
                object-fit: contain;
            }
        }


        .mechanical-design-content {
            padding-left: 71px;
            padding-right: 73px;

            .mechanical-design-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                color: #FFFFFF;
                margin-bottom: 80px;
            }

            .mechanical-design-list {
                margin-bottom: 34.11px;

                .mechanical-design-list-item {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 36px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                    display: flex;
                    align-items: flex-start;

                    &::before {
                        content: "•";
                        position: absolute;
                        left: 0;
                        color: #FFFFFF;
                        font-size: 20px;
                    }
                }

            }

            .mechanical-design-flowchart {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding-bottom: 131.61px;
                border-bottom: 1px solid #FFFFFF;
            }

            .tool-design-development {
                display: flex;
                flex-direction: column;
                align-items: center;

                .tool-design-title {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 48px;
                    letter-spacing: 0%;
                    text-align: center;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 51px;
                    margin-top: 96.5px;
                }

                img {
                    margin-bottom: 46px;

                }

                .tool-design-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                }

                .tool-design-list {
                    padding-bottom: 142px;
                    border-bottom: 1px solid #FFFFFF;

                    .tool-design-list-item {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: justify;
                        color: #FFFFFF;
                        display: flex;
                        align-items: flex-start;

                        &::before {
                            content: "•";
                            position: absolute;
                            left: 0;
                            color: #FFFFFF;
                            font-size: 20px;
                        }
                    }
                }

                .simulation-analysis {
                    margin-top: 80.5px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .simulation-analysis-title {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 48px;

                        letter-spacing: 0%;
                        text-align: center;
                        background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 65px;
                    }

                    .simulation-analysis-description {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: justify;
                        color: #FFFFFF;
                        margin-bottom: 50px;
                    }

                    .simulation-analysis-images {
                        display: flex;
                        flex-direction: column;
                        align-self: center;

                        .simulation-analysis-subtitle {
                            bottom: 0;
                            font-family: Poppins;
                            font-weight: 500;
                            font-size: 24px;

                            letter-spacing: 0%;
                            text-align: center;
                            background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                            background-clip: text;
                            -webkit-text-fill-color: transparent;
                            margin-bottom: 70px;
                        }
                    }

                    .simulation-analysis-description-box {
                        .simulation-analysis-testing {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 42px;

                            .simulation-analysis-testing-title {
                                font-family: Poppins;
                                font-weight: 600;
                                font-size: 20px;

                                line-height: 30px;
                                letter-spacing: 0%;
                                vertical-align: middle;
                                color: #FFFFFF;
                                margin-bottom: 15px;
                            }
                        }
                    }
                }

                .how-it-works {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-top: 112px;

                    .how-it-works-title {
                        font-family: Poppins;
                        font-weight: 500;
                        font-size: 60px;

                        letter-spacing: 0%;
                        background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 40px;
                    }
                }
            }
        }
    }
}