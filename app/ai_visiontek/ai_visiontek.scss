.ai-vision-tek-page-container {
    .ai-vision-tek-page-content {
        .ai-vision-tek-header-image {
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 60px;

            img {
                width: 100%;
                max-width: 100%;
                height: auto;
            }
        }

        .ai-vision-tek-content {
            padding-left: 95px;
            padding-right: 95px;
            margin-bottom: 95px;

            .ai-vision-tek-title {
                margin-bottom: 33px;
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                background: #8CFFE4;
                background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .ai-vision-tek-description-container {
                display: flex;
                flex-direction: column;
                gap: 55px;
                margin-bottom: 118px;

                .ai-vision-tek-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;

                }
            }

            .ai-ml {
                margin-bottom: 76px;

                .ai-ml-title {
                    font-family: Poppins;
                    font-weight: 300;
                    font-size: 34px;

                    letter-spacing: 0%;
                    background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    margin-bottom: 54px;
                }

                .ai-ml-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;


                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;
                    margin-bottom: 40px;
                }

                .ai-ml-tech-stack {
                    display: flex;
                    gap: 139px;
                    margin-left: 20px;

                    ul {
                        li {
                            font-family: Poppins;
                            font-weight: 400;
                            font-size: 25px;

                            line-height: 40px;
                            letter-spacing: 0%;
                            color: #FFFFFF;
                        }
                    }
                }
            }



        }



        .our-offerings {
            // padding-left: 91px;
            // padding-right: 110px;
            margin-bottom: 54px;

            .our-offerings-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 41px;
            }

            .our-offerings-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 34px;
            }

            .our-offerings-list {
                display: flex;
                flex-direction: column;
                gap: 15px;

                .offering-item-wrapper {
                    display: flex;
                    gap: 15px;
                }

                .offering-item {
                    padding: 38.09px 27.41px 39.58px 34px;
                    width: 100%;
                    min-height: 320px;
                    border: 1px solid #2499E280;
                    color: #FFFFFF;

                    .offering-item-title {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 22px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: center;
                        background: #8CFFE4;
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 30.48px;

                    }
                }
            }
        }

        .success-stories {
            margin-bottom: 98px;

            .success-stories-description {
                font-family: Poppins;
                font-weight: 500;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 30px;
            }

            .bullets-point {
                li {
                    display: flex;
                    flex-direction: row !important;
                    align-items: start;
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    color: #FFFFFF;
                    display: flex;
                    flex-direction: column;
                    gap: 30px;
                }

                & li::before {
                    content: "•";
                    color: #FFFFFF;
                    font-size: 20px;
                    // margin-right: 10px;
                }
            }
        }

        // .case-study {
        //     padding-left: 93px;
        //     padding-right: 103px;
        //     margin-bottom: 301px;

        //     .case-study-title {
        //         font-family: Poppins;
        //         font-weight: 400;
        //         font-size: 34px;

        //         letter-spacing: 0%;
        //         text-align: center;
        //         background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
        //         background-clip: text;
        //         -webkit-text-fill-color: transparent;
        //         margin-bottom: 61px;
        //     }

        //     .case-study-images {
        //         display: flex;
        //         justify-content: center;
        //         align-items: center;
        //         gap: 59px;
        //         margin-bottom: 77.17px;

        //         img {
        //             width: 100%;
        //             max-width: 270px;
        //             min-height: 200px;
        //             border-radius: 10px;
        //         }

        //         .case-study-image-item {
        //             display: flex;
        //             flex-direction: column;
        //             align-items: center;
        //             gap: 44px;

        //             .image-title {
        //                 font-family: Montserrat;
        //                 font-weight: 400;
        //                 font-size: 24px;

        //                 line-height: 30px;
        //                 letter-spacing: 0%;
        //                 vertical-align: bottom;
        //                 color: #FFFFFF;
        //             }
        //         }
        //     }

        //     .case-study-description {
        //         .case-study-text {
        //             font-family: Poppins;
        //             font-weight: 400;
        //             font-style: italic;
        //             font-size: 16px;

        //             line-height: 30px;
        //             letter-spacing: 0%;
        //             color: #FFFFFF;
        //         }
        //     }
        // }

        .case-study {
            margin-top: 160px;
            padding-left: 93px;
            padding-right: 103px;
            margin-bottom: 301px;

            .case-study-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                text-align: center;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 61px;
            }

            .case-study-images {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 59px;
                margin-bottom: 77.17px;

                img {
                    width: 100%;
                    max-width: 270px;
                    min-height: 200px;
                    border-radius: 10px;
                    transition: all 0.3s ease-in-out;
                }

                .case-study-image-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 44px;
                    transition: all 0.3s ease-in-out;
                    cursor: pointer;
                    padding: 10px;
                    border-radius: 12px;

                    &:hover {
                        transform: translateY(-10px);
                        background: rgba(140, 255, 228, 0.05);

                        img {
                            transform: scale(1.05);
                            box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
                        }

                        .image-title {
                            background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                            background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }
                    }

                    img {
                        transition: all 0.3s ease-in-out;
                    }

                    .image-title {
                        font-family: Montserrat;
                        font-weight: 400;
                        font-size: 24px;

                        line-height: 30px;
                        letter-spacing: 0%;
                        vertical-align: bottom;
                        color: #FFFFFF;
                    }
                }
            }

            .case-study-description {
                .case-study-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-style: italic;
                    font-size: 16px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    color: #FFFFFF;
                }
            }
        }
    }
}