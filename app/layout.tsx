import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Montserrat, Port_Lligat_Sans } from "next/font/google";
import "./globals.scss";
import Navbar from "@/components/navbar/navbar";
import Footer from "@/components/footer/Footer";
import { Providers } from "./providers";
import ScrollToTop from "@/components/ScrollToTop/ScrollToTop";

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

const prata = Prata({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-prata",
});

const montserrat = Montserrat({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-montserrat",
});

const portLligatSans = Port_Lligat_Sans({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-port-lligat-sans",
});

export const metadata: Metadata = {
  title: "Aadvik Teklabs",
  description: "Aadvik Teklabs - Innovating Tomorrow's Technology Today",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${prata.variable} ${montserrat.variable} ${portLligatSans.variable}`}
    >
      <body style={{ background: "#021F2E" }} className={`antialiased`}>
        <Providers>
          <Navbar />
          {children}
          <ScrollToTop />
          <Footer />
        </Providers>
      </body>
    </html>
  );
}
