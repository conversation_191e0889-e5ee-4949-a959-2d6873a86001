.smart-home-container {
  .smart-home-page-content {
    .smart-home-header-image {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 60px;
      position: relative;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        display: block;
      }

      .banner-text-overlay {
        position: absolute;
        top: 90%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 2;
        padding: 0 20px;
        width: 100%;
        max-width: 90vw;

        .banner-title {
          color: #ffffff;
          font-size: 5rem;
          font-weight: 700;
          text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
          margin: 0;
          letter-spacing: 1px;

          @media (max-width: 1024px) {
            font-size: 3.5rem;
          }

          @media (max-width: 768px) {
            font-size: 2.5rem;
          }

          @media (max-width: 480px) {
            font-size: 2rem;
          }
        }
      }
    }

    @media (max-width: 768px) {
      .smart-home-header-image {
        margin-bottom: 40px;

        img {
          border-radius: 6px;
        }
      }
    }
  }

  .smart-home-subtitle {
    padding: 0 70px;
    margin-bottom: 80px;

    .subtitle-one {
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
    }

    .subtitle-two {
      margin-top: 50px;
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;

      .bullet-list {
        list-style-type: disc;
        padding-left: 20px;
        margin: 10px 0;

        li {
          margin-bottom: 10px;
          color: #ffffff;
        }
      }
    }
    .subtitle-three {
      margin-top: 30px;
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #ffffff;
    }
  }

  .automation-system-design {
    padding: 0 70px;
    margin-bottom: 80px;

    .automation-system-design-title {
      font-family: Edu NSW ACT Foundation;
      font-weight: 400;
      font-size: 48px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
      color: #ffffff;
    }
    .automation-system-design-subtitle {
      margin-top: 50px;
      font-family: Poppins;
      font-weight: 400;
      font-size: 20px;
      line-height: 30px;
      letter-spacing: 0%;
      text-align: justify;
      vertical-align: middle;
      color: #FFFFFF;
    }
    .automation-system-design-subtitle-two {
        margin-top: 60px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #FFFFFF;
    }
  }

  .smart-home-image-container {
    margin: 57px 70px 70px 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      max-width: 100%;
      height: auto;
    }
  }
}
