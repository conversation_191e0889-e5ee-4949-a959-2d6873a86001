.web-technologies-page-container {
    .web-technologies-page-content {
        .web-technologies-header-image {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .web-technologies-content {
            padding-left: 90px;
            padding-right: 109px;
            margin-bottom: 95px;

            .web-technologies-title {
                margin-bottom: 33px;
                font-family: Poppins;
                font-weight: 400;
                font-size: 36px;

                line-height: 30px;
                letter-spacing: 0%;
                background: #8CFFE4;
                background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .web-technologies-description-container {
                display: flex;
                flex-direction: column;
                gap: 55px;
                margin-bottom: 118px;

                .web-technologies-description {
                    font-family: Poppins;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 30px;
                    letter-spacing: 0%;
                    text-align: justify;
                    color: #FFFFFF;

                }
            }

            .web-technologies-images {
                display: flex;
                gap: 15px;
                justify-content: center;
                align-items: center;
            }


        }

        .our-offerings {
            padding-left: 91px;
            padding-right: 110px;
            margin-bottom: 54px;

            .our-offerings-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 14px;
            }

            .our-offerings-description {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 30px;
                letter-spacing: 0%;
                text-align: justify;
                vertical-align: middle;
                color: #FFFFFF;
                margin-bottom: 34px;
            }

            .our-offerings-list {
                display: flex;
                flex-direction: column;
                gap: 15px;

                .offering-item-wrapper {
                    display: flex;
                    gap: 15px;
                }

                .offering-item {
                    padding: 38.09px 27.41px 39.58px 34px;
                    width: 100%;
                    min-height: 320px;
                    border: 1px solid #2499E280;
                    color: #FFFFFF;

                    .offering-item-title {
                        font-family: Poppins;
                        font-weight: 400;
                        font-size: 22px;
                        line-height: 30px;
                        letter-spacing: 0%;
                        text-align: center;
                        background: #8CFFE4;
                        background-clip: text;
                        -webkit-text-fill-color: transparent;
                        margin-bottom: 30.48px;

                    }
                }
            }
        }

        .our-expanded-services-includes {
            padding-left: 90px;
            padding-right: 110px;

            .our-expanded-services-title {
                font-family: Poppins;
                font-weight: 300;
                font-size: 34px;

                letter-spacing: 0%;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 54px;

            }

           img{
            width: 100%;
           }
        }

        // .case-study {
        //     padding-left: 93px;
        //     padding-right: 103px;
        //     margin-bottom: 301px;

        //     .case-study-title {
        //         font-family: Poppins;
        //         font-weight: 400;
        //         font-size: 34px;

        //         letter-spacing: 0%;
        //         text-align: center;
        //         background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
        //         background-clip: text;
        //         -webkit-text-fill-color: transparent;
        //         margin-bottom: 61px;
        //     }

        //     .case-study-images {
        //         display: flex;
        //         justify-content: center;
        //         align-items: center;
        //         gap: 59px;
        //         margin-bottom: 77.17px;

        //         img {
        //             width: 100%;
        //             max-width: 270px;
        //             min-height: 200px;
        //             border-radius: 10px;
        //         }

        //         .case-study-image-item {
        //             display: flex;
        //             flex-direction: column;
        //             align-items: center;
        //             gap: 44px;

        //             .image-title {
        //                 font-family: Montserrat;
        //                 font-weight: 400;
        //                 font-size: 24px;

        //                 line-height: 30px;
        //                 letter-spacing: 0%;
        //                 vertical-align: bottom;
        //                 color: #FFFFFF;
        //             }
        //         }
        //     }

        //     .case-study-description {
        //         .case-study-text {
        //             font-family: Poppins;
        //             font-weight: 400;
        //             font-style: italic;
        //             font-size: 16px;

        //             line-height: 30px;
        //             letter-spacing: 0%;
        //             color: #FFFFFF;
        //         }
        //     }
        // }

        .case-study {
            margin-top: 160px;
            padding-left: 93px;
            padding-right: 103px;
            margin-bottom: 301px;

            .case-study-title {
                font-family: Poppins;
                font-weight: 400;
                font-size: 34px;

                letter-spacing: 0%;
                text-align: center;
                background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-bottom: 61px;
            }

            .case-study-images {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 59px;
                margin-bottom: 77.17px;

                img {
                    width: 100%;
                    max-width: 270px;
                    min-height: 200px;
                    border-radius: 10px;
                    transition: all 0.3s ease-in-out;
                }

                .case-study-image-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 44px;
                    transition: all 0.3s ease-in-out;
                    cursor: pointer;
                    padding: 10px;
                    border-radius: 12px;

                    &:hover {
                        transform: translateY(-10px);
                        background: rgba(140, 255, 228, 0.05);

                        img {
                            transform: scale(1.05);
                            box-shadow: 0 10px 20px rgba(140, 255, 228, 0.2);
                        }

                        .image-title {
                            background: linear-gradient(180deg, #8CFFE4 37.36%, #549989 100%);
                            background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }
                    }

                    img {
                        transition: all 0.3s ease-in-out;
                    }

                    .image-title {
                        font-family: Montserrat;
                        font-weight: 400;
                        font-size: 24px;

                        line-height: 30px;
                        letter-spacing: 0%;
                        vertical-align: bottom;
                        color: #FFFFFF;
                    }
                }
            }

            .case-study-description {
                .case-study-text {
                    font-family: Poppins;
                    font-weight: 400;
                    font-style: italic;
                    font-size: 16px;

                    line-height: 30px;
                    letter-spacing: 0%;
                    color: #FFFFFF;
                }
            }
        }
    }
}